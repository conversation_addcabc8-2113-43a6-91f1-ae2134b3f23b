import express, { type Request, Response, NextFunction } from "express";
import { createServer } from "http";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import cors from 'cors';
import logger from "./utils/logger";
import monitoringRoutes from './routes/monitoring-routes';
import { monitoring } from './services/monitoring';
import { authenticationMiddleware, securityHeadersMiddleware } from './middleware/authentication';
import WebSocketChatServer from './ws-server';

// Import db from the updated db.ts file that uses schema-resolver
import db from './db';

// Initialize required services
import { twilioSMSService } from './services/twilio-sms-service';
import { initializeEmailService } from './services/email-service';

// Enable Redis fallback when Redis connection details aren't provided
if (!process.env.REDIS_HOST) {
  process.env.SKIP_REDIS = 'true';
  logger.info('No Redis host configured, using in-memory fallback');
}

// Initialize express app
const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Apply consolidated authentication middleware
app.use(authenticationMiddleware);

// Apply security headers middleware
app.use(securityHeadersMiddleware);

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      // Use our structured logger instead of basic logging
      const context: {
        method: string;
        path: string;
        statusCode: number;
        duration: string;
        ip: string | undefined;
        userAgent: string | undefined;
        response?: Record<string, any>;
      } = {
        method: req.method,
        path: path,
        statusCode: res.statusCode,
        duration: `${duration}ms`,
        ip: req.ip,
        userAgent: req.get('user-agent')
      };

      // Add response data to logs for non-success status codes or in development
      if (res.statusCode >= 400 || process.env.NODE_ENV !== 'production') {
        if (capturedJsonResponse) {
          context.response = capturedJsonResponse;
        }
      }

      // Log with appropriate level based on status code
      if (res.statusCode >= 500) {
        logger.error(`API error: ${req.method} ${path}`, null, context);
      } else if (res.statusCode >= 400) {
        logger.warn(`API warning: ${req.method} ${path}`, context);
      } else {
        logger.info(`API request: ${req.method} ${path}`, context);
      }
    }
  });

  next();
});

// Add monitoring routes before other routes
app.use('/api/metrics', monitoringRoutes);

// Track all requests
app.use((req, res, next) => {
  const start = performance.now();
  res.on('finish', () => {
    const duration = performance.now() - start;
    monitoring.trackRequest(req.path, duration, res.statusCode);
  });
  next();
});

(async () => {
  // Initialize email service
  try {
    await initializeEmailService();
    logger.info('Email service successfully initialized');
  } catch (error) {
    logger.warn('Failed to initialize email service, some functionality may be limited', error);
  }
  
  // Initialize queue consumers with in-memory fallback
  try {
    const { initializeQueueConsumers } = await import('./services/queue-consumers');
    await initializeQueueConsumers();
    logger.info('Queue consumers successfully initialized');
  } catch (error) {
    logger.warn('Failed to initialize queue consumers, will use in-memory fallback', error);
  }

  // Create HTTP server
  const httpServer = createServer(app);
  
  // Register routes
  await registerRoutes(app);
  
  // Initialize WebSocket server
  const chatServer = new WebSocketChatServer();
  chatServer.initialize(httpServer);
  
  // Use the HTTP server instead of the Express app for listening
  const server = httpServer;

  // Simple error handler
  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    // Log error details
    console.error(`[ERROR] ${err.stack || err}`);

    res.status(status).json({ 
      message: message,
      success: false
    });
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    console.log('Server running on port 5000 (Authentication bypassed)');
  });
})();