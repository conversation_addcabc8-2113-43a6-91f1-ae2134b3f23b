import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import logger from './utils/logger';

// Import from main schema file temporarily to avoid schema-resolver issues
import * as schema from '../shared/schema';

// Database connection configuration
const connectionString = process.env.DATABASE_URL || 'postgres://postgres:postgres@localhost:5432/rylie';

// Create postgres client
const client = postgres(connectionString, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
  prepare: false,
});

logger.info(`Connecting to database: ${connectionString.split('@')[1]}`);

// Create drizzle instance with all schema tables
const db = drizzle(client, { schema });

export default db;