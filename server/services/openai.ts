import OpenAI from "openai";
import { ChatCompletionMessageParam } from "openai/resources/chat/completions";

// Initialize OpenAI lazily to prevent startup failures
let openai: OpenAI | null = null;

function getOpenAI(): OpenAI {
  if (!openai) {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OpenAI API key not configured');
    }
    openai = new OpenAI({ apiKey });
    console.log("OpenAI API configured successfully");
  }
  return openai;
}

// Generate AI response using OpenAI
export async function generateAIResponse(prompt: string, customerScenario?: string): Promise<string> {
  try {
    // Construct the message to send to OpenAI
    const messages: ChatCompletionMessageParam[] = [
      {
        role: "system",
        content: prompt
      }
    ];

    // Add customer message if provided
    if (customerScenario) {
      messages.push({
        role: "user",
        content: customerScenario
      });
    } else {
      // Default customer message if none provided
      messages.push({
        role: "user",
        content: "Hello, I'm interested in learning more about your vehicles."
      });
    }

    // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await getOpenAI().chat.completions.create({
      model: "gpt-4o",
      messages: messages,
      temperature: 0.7,
      max_tokens: 800,
      response_format: { type: "json_object" }
    });

    // Parse the JSON response
    const responseContent = response.choices[0].message.content || "{}";

    try {
      const jsonResponse = JSON.parse(responseContent);
      // Extract the answer field from the JSON response if it exists
      if (jsonResponse.answer) {
        return jsonResponse.answer;
      } else {
        // If no answer field, just return the full content
        return responseContent;
      }
    } catch (parseError) {
      // If not valid JSON, return the raw content
      return responseContent;
    }
  } catch (error) {
    console.error('Error generating AI response:', error);
    throw error;
  }
}

// Generate handover dossier for sales team
export async function generateHandoverDossier(conversationHistory: string, customerScenario: string): Promise<any> {
  try {
    // Construct the prompt for the handover dossier
    const systemPrompt = `Generate a sales lead handover dossier based on the conversation with a customer.
    Format the response as a JSON object with the following structure:
    {
      "customerName": "Name or Anonymous if unknown",
      "customerContact": "Contact details if available, otherwise 'Not provided'",
      "conversationSummary": "Brief summary of the conversation",
      "customerInsights": [
        {"key": "Insight category", "value": "Specific insight", "confidence": 0.0-1.0}
      ],
      "vehicleInterests": [
        {"make": "Vehicle make", "model": "Model name", "year": year, "confidence": 0.0-1.0}
      ],
      "suggestedApproach": "Recommended approach for sales team",
      "urgency": "low|medium|high",
      "escalationReason": "Reason for handover"
    }`;

    // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await getOpenAI().chat.completions.create({
      model: "gpt-4o",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: customerScenario }
      ],
      temperature: 0.5,
      max_tokens: 1000,
      response_format: { type: "json_object" }
    });

    // Parse the response content as JSON
    const dossierContent = response.choices[0].message.content;
    return dossierContent ? JSON.parse(dossierContent) : {};
  } catch (error) {
    console.error('Error generating handover dossier:', error);
    throw error;
  }
}

// Generate response analysis
export async function generateResponseAnalysis(prompt: string, customerScenario: string): Promise<any> {
  try {
    const systemPrompt = `Analyze this customer interaction for a car dealership.
    Format the response as a JSON object with the following structure:
    {
      "customerName": "Name if detected, otherwise 'Unknown'",
      "query": "Main customer query",
      "analysis": "Analysis of customer's intent and needs",
      "insights": "Key insights about the customer's situation",
      "channel": "chat",
      "salesReadiness": "low|medium|high",
      "handoverNeeded": true|false
    }`;

    // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await getOpenAI().chat.completions.create({
      model: "gpt-4o",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: customerScenario }
      ],
      temperature: 0.5,
      max_tokens: 500,
      response_format: { type: "json_object" }
    });

    // Parse the response content as JSON
    const analysisContent = response.choices[0].message.content;
    return analysisContent ? JSON.parse(analysisContent) : {};
  } catch (error) {
    console.error('Error generating response analysis:', error);
    throw error;
  }
}

export default {
  generateAIResponse,
  generateHandoverDossier,
  generateResponseAnalysis
};